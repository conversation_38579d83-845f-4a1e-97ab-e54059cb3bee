/**
 * Standardized Form Handler for GRS Application
 * Provides consistent AJAX form submission and validation across all forms
 */

// Form configuration for different pages
const FORM_CONFIGS = {
    'geometry-form': {
        endpoint: '/geometry',
        localStorageKey: 'geometryData',
        fields: ['wall-height', 'embedment-depth', 'wall-length', 'wall-batter', 'backslope-angle', 'backslope-rise'],
        onSuccess: function(data) {
            // Redraw canvas after successful save to prevent image disappearing
            if (typeof redrawCanvas === 'function') {
                setTimeout(() => redrawCanvas(), 100);
            }
        }
    },
    'reinforcedsoil-form': {
        endpoint: '/reinforcedsoil',
        localStorageKey: 'reinforcedsoilData',
        fields: ['reinforced-density', 'reinforced-friction', 'reinforced-cohesion']
    },
    'retainedsoil-form': {
        endpoint: '/retainedsoil',
        localStorageKey: 'retainedsoilData',
        fields: ['retained-density', 'retained-friction', 'retained-cohesion']
    },
    'foundationsoil-form': {
        endpoint: '/foundationsoil',
        localStorageKey: 'foundationsoilData',
        fields: ['foundationsoildensity', 'foundationsoilfriction-angle', 'foundationsoilcohesion', 'eccentricity', 'eccentricity-seismic', 'watertable']
    },

    'reinforcementproperties-form': {
        endpoint: '/reinforcementproperties',
        localStorageKey: 'reinforcementData',
        fields: ['reinforcement-type', 'tensile-strength', 'coverage-ratio']
    },
    'externalloadsform': {
        endpoint: '/externalloads',
        localStorageKey: 'externalLoadsData',
        fields: [] // Dynamic fields handled by the form itself
    },
    'reinforcementlayout-form': {
        endpoint: '/reinforcementlayout',
        localStorageKey: 'reinforcementLayoutData',
        fields: [] // Dynamic fields handled by the form itself
    },
    'project-info-form': {
        endpoint: '/project_info',
        localStorageKey: 'projectInfo',
        fields: ['project-name', 'project-id', 'designer', 'client', 'description', 'date', 'revision']
    }
};

// Initialize form handlers when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeFormHandlers();
});

function initializeFormHandlers() {
    console.log('Initializing standardized form handlers...');

    // Initialize each configured form
    Object.keys(FORM_CONFIGS).forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            // Remove existing handler flag to allow reinitialization
            form.removeAttribute('data-form-handler-attached');
            setupFormHandler(form, FORM_CONFIGS[formId]);
            console.log(`✅ Initialized form handler for: ${formId}`);
        } else {
            console.log(`❌ Form not found: ${formId}`);
        }
    });
}

function setupFormHandler(form, config) {
    // Forms that have special handling but still need localStorage integration
    const specialForms = ['reinforcementlayout-form', 'externalloadsform', 'reinforcementproperties-form'];
    if (specialForms.includes(form.id)) {
        console.log(`Setting up localStorage integration for special form: ${form.id}`);
        // Load saved data from localStorage
        loadFormData(form, config);
        // Set up localStorage integration without interfering with existing handlers
        setupLocalStorageIntegration(form);
        return;
    }

    // Load saved data from localStorage
    loadFormData(form, config);

    // Add real-time validation (but don't replace existing listeners)
    addValidationListeners(form);

    // Only add form submission handler if one doesn't already exist
    if (!form.hasAttribute('data-form-handler-attached')) {
        console.log(`🔧 Attaching submit handler to form: ${form.id}`);
        form.addEventListener('submit', function(event) {
            console.log(`🚀 Form submission intercepted: ${form.id}`);
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            handleFormSubmission(form, config);
        }, true); // Use capture mode to ensure this runs first
        form.setAttribute('data-form-handler-attached', 'true');
        console.log(`✅ Submit handler attached to: ${form.id}`);
    } else {
        console.log(`⚠️ Form handler already attached to: ${form.id}`);
    }
}

function loadFormData(form, config) {
    if (!config.localStorageKey) return;

    try {
        const savedData = JSON.parse(localStorage.getItem(config.localStorageKey));
        if (savedData) {
            // Special handling for geometry form - map localStorage keys to form field IDs
            if (form.id === 'geometry-form') {
                const fieldMapping = {
                    'wallHeight': 'wall-height',
                    'embedmentDepth': 'embedment-depth',
                    'wallLength': 'wall-length',
                    'wallBatter': 'wall-batter',
                    'backslopeAngle': 'backslope-angle',
                    'backslopeRise': 'backslope-rise'
                };

                Object.keys(fieldMapping).forEach(dataKey => {
                    const fieldId = fieldMapping[dataKey];
                    const field = form.querySelector(`#${fieldId}`);
                    if (field && savedData[dataKey] !== undefined) {
                        field.value = savedData[dataKey];
                    }
                });
            } else if (form.id === 'externalloadsform' || form.id === 'reinforcementlayout-form' || form.id === 'reinforcementproperties-form') {
                // For forms with dynamic fields, load all saved values
                Object.keys(savedData).forEach(key => {
                    const field = form.querySelector(`[name="${key}"], #${key}`);
                    if (field && savedData[key] !== undefined) {
                        if (field.type === 'checkbox') {
                            field.checked = savedData[key];
                        } else {
                            field.value = savedData[key];
                        }
                    }
                });
            } else {
                // Standard handling for other forms
                config.fields.forEach(fieldId => {
                    const field = form.querySelector(`#${fieldId}`);
                    if (field && savedData[fieldId] !== undefined) {
                        field.value = savedData[fieldId];
                    }
                });
            }
            console.log(`Loaded data for ${form.id} from localStorage`);
        }
    } catch (error) {
        console.error(`Error loading data for ${form.id}:`, error);
    }
}

function saveFormDataToLocalStorage(form) {
    const config = FORM_CONFIGS[form.id];
    if (!config || !config.localStorageKey) return;

    try {
        const formObject = {};

        // Special handling for geometry form - use camelCase keys for localStorage
        if (form.id === 'geometry-form') {
            const fieldMapping = {
                'wall-height': 'wallHeight',
                'embedment-depth': 'embedmentDepth',
                'wall-length': 'wallLength',
                'wall-batter': 'wallBatter',
                'backslope-angle': 'backslopeAngle',
                'backslope-rise': 'backslopeRise'
            };

            Object.keys(fieldMapping).forEach(fieldId => {
                const field = form.querySelector(`#${fieldId}`);
                if (field) {
                    const dataKey = fieldMapping[fieldId];
                    formObject[dataKey] = field.value;
                }
            });
        } else if (form.id === 'externalloadsform' || form.id === 'reinforcementlayout-form' || form.id === 'reinforcementproperties-form') {
            // For forms with dynamic fields, save all input values
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.name || input.id) {
                    const key = input.name || input.id;
                    if (input.type === 'checkbox') {
                        formObject[key] = input.checked;
                    } else {
                        formObject[key] = input.value;
                    }
                }
            });
        } else {
            // Standard handling for other forms
            config.fields.forEach(fieldId => {
                const field = form.querySelector(`#${fieldId}`);
                if (field) {
                    formObject[fieldId] = field.value;
                }
            });
        }

        localStorage.setItem(config.localStorageKey, JSON.stringify(formObject));
        console.log(`Real-time saved data for ${form.id} to localStorage`);
    } catch (error) {
        console.error(`Error saving data for ${form.id} to localStorage:`, error);
    }
}

function setupLocalStorageIntegration(form) {
    // Use event delegation to capture input changes without interfering with existing handlers
    // This works by listening at the form level for bubbled events

    const saveToLocalStorage = () => {
        // Use a small delay to ensure the value has been updated by existing handlers
        setTimeout(() => {
            saveFormDataToLocalStorage(form);
        }, 50);
    };

    // Only add if not already added
    if (!form.hasAttribute('data-localstorage-integration-attached')) {
        form.addEventListener('input', saveToLocalStorage, true); // Use capture phase
        form.addEventListener('change', saveToLocalStorage, true); // Use capture phase
        form.addEventListener('blur', saveToLocalStorage, true); // Use capture phase
        form.setAttribute('data-localstorage-integration-attached', 'true');
        console.log(`✅ LocalStorage integration attached to ${form.id}`);
    }
}

function addValidationListeners(form) {
    const inputs = form.querySelectorAll('input, select, textarea');

    // Forms that have their own dynamic updating and input handlers
    const formsWithOwnHandlers = ['geometry-form', 'externalloadsform', 'reinforcementlayout-form', 'reinforcementproperties-form'];

    if (formsWithOwnHandlers.includes(form.id)) {
        // For forms with their own handlers, we'll add a global listener to capture their changes
        // and save to localStorage without interfering with their existing functionality
        console.log(`Setting up localStorage integration for ${form.id} with existing handlers`);
        setupLocalStorageIntegration(form);
        return;
    }

    // For other forms, add our standard listeners
    inputs.forEach(input => {
        // Only add listeners if not already added
        if (!input.hasAttribute('data-validation-attached')) {
            input.addEventListener('input', () => {
                validateField(input);
                // Save form data to localStorage in real-time
                saveFormDataToLocalStorage(form);
                // Trigger visualization updates for specific forms
                triggerVisualizationUpdate(form.id);
            });
            input.addEventListener('blur', () => {
                validateField(input);
                // Save form data to localStorage on blur as well
                saveFormDataToLocalStorage(form);
            });
            input.addEventListener('change', () => {
                // Save form data to localStorage on change (for dropdowns, checkboxes, etc.)
                saveFormDataToLocalStorage(form);
            });
            input.setAttribute('data-validation-attached', 'true');
        }
    });
}

function triggerVisualizationUpdate(formId) {
    // Skip visualization updates for forms that have their own dynamic updating
    const formsWithOwnUpdating = ['geometry-form', 'externalloadsform', 'reinforcementlayout-form', 'reinforcementproperties-form'];
    if (formsWithOwnUpdating.includes(formId)) {
        console.log(`Skipping form handler visualization update for ${formId} - has own dynamic updating`);
        return;
    }

    // Trigger real-time visualization updates for other forms if needed
    console.log(`No visualization update needed for form: ${formId}`);
}

function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Required validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    }
    
    // Numeric validation
    if (field.type === 'number' && value) {
        if (isNaN(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid number';
        } else {
            const numValue = parseFloat(value);
            if (field.min && numValue < parseFloat(field.min)) {
                isValid = false;
                errorMessage = `Value must be at least ${field.min}`;
            }
            if (field.max && numValue > parseFloat(field.max)) {
                isValid = false;
                errorMessage = `Value must be at most ${field.max}`;
            }
        }
    }
    
    // Update field styling
    field.classList.toggle('is-invalid', !isValid);
    field.classList.toggle('invalid', !isValid); // Alternative class name
    field.classList.toggle('is-valid', isValid && value);
    
    // Show/hide error message
    let errorElement = field.parentNode.querySelector('.invalid-feedback, .error-message');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        field.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = errorMessage;
    errorElement.style.display = errorMessage ? 'block' : 'none';
    
    return isValid;
}

function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function handleFormSubmission(form, config) {
    console.log(`Submitting form: ${form.id}`);
    
    // Validate form
    if (!validateForm(form)) {
        showErrorPopup('Please fix the errors in the form before submitting.');
        return;
    }
    
    // Prepare form data
    const formData = new FormData(form);
    
    // Show loading state
    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
    const originalText = submitButton ? submitButton.textContent : '';
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.textContent = 'Saving...';
    }
    
    // Submit form
    fetch(config.endpoint, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Data is already saved to localStorage in real-time, so we don't need to save it again here
        // Just ensure the latest data is saved (in case there were any changes during submission)
        saveFormDataToLocalStorage(form);
        
        // Show success message
        if (data.message) {
            console.log(`✅ FormHandler.js: Showing success popup for ${form.id}`);
            showSuccessPopup(data.message);

            // Update sidebar status indicator
            updateSidebarStatus(form.id);
        }
        
        // Call success callback
        if (config.onSuccess) {
            config.onSuccess(data);
        }
        
        console.log(`Form ${form.id} submitted successfully`);
    })
    .catch(error => {
        console.error(`Error submitting ${form.id}:`, error);
        showErrorPopup('Error saving data: ' + error.message);
    })
    .finally(() => {
        // Restore button state
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    });
}

// Popup/Modal Functions
function showSuccessPopup(message) {
    showPopup(message, 'success');
}

function showErrorPopup(message) {
    showPopup(message, 'error');
}

function showPopup(message, type = 'success') {
    // Remove any existing popup
    const existingPopup = document.getElementById('grs-popup');
    if (existingPopup) {
        existingPopup.remove();
    }

    // Create popup HTML
    const popup = document.createElement('div');
    popup.id = 'grs-popup';
    popup.className = `grs-popup grs-popup-${type}`;

    popup.innerHTML = `
        <div class="grs-popup-overlay" onclick="closePopup()"></div>
        <div class="grs-popup-content">
            <div class="grs-popup-header">
                <h3>${type === 'success' ? '✅ Success' : '❌ Error'}</h3>
                <button class="grs-popup-close" onclick="closePopup()">&times;</button>
            </div>
            <div class="grs-popup-body">
                <p>${message}</p>
            </div>
            <div class="grs-popup-footer">
                <button class="grs-popup-btn grs-popup-btn-primary" onclick="closePopup()">OK</button>
            </div>
        </div>
    `;

    // Add to document
    document.body.appendChild(popup);

    // Show popup with animation
    setTimeout(() => {
        popup.classList.add('grs-popup-show');
    }, 10);

    // Auto-close after 5 seconds for success messages
    if (type === 'success') {
        setTimeout(() => {
            closePopup();
        }, 5000);
    }
}

function closePopup() {
    const popup = document.getElementById('grs-popup');
    if (popup) {
        popup.classList.remove('grs-popup-show');
        setTimeout(() => {
            popup.remove();
        }, 300);
    }
}

// Update sidebar status indicator
function updateSidebarStatus(formId) {
    const formToRouteMap = {
        'geometry-form': '/geometry',
        'reinforcedsoil-form': '/reinforcedsoil',
        'retainedsoil-form': '/retainedsoil',
        'foundationsoil-form': '/foundationsoil',
        'externalloadsform': '/externalloads',
        'reinforcementproperties-form': '/reinforcementproperties',
        'reinforcementlayout-form': '/reinforcementlayout',
        'project-info-form': '/project_info'
    };

    const route = formToRouteMap[formId];
    if (route) {
        const menuLink = document.querySelector(`a[href="${route}"]`);
        if (menuLink) {
            // Check if status icon already exists
            let statusIcon = menuLink.querySelector('.status-icon');
            if (!statusIcon) {
                // Create new status icon
                statusIcon = document.createElement('i');
                statusIcon.className = 'fas fa-check-circle status-icon saved';
                menuLink.appendChild(statusIcon);
            } else {
                // Update existing icon
                statusIcon.className = 'fas fa-check-circle status-icon saved';
            }
            console.log(`✅ Updated sidebar status for ${route}`);
        }
    }
}

// Make functions available globally for AJAX reinitialization
window.initializeFormHandlers = initializeFormHandlers;
window.setupFormHandler = setupFormHandler;
window.handleFormSubmission = handleFormSubmission;
window.saveFormDataToLocalStorage = saveFormDataToLocalStorage;
window.setupLocalStorageIntegration = setupLocalStorageIntegration;
window.FORM_CONFIGS = FORM_CONFIGS;
window.showSuccessPopup = showSuccessPopup;
window.showErrorPopup = showErrorPopup;
window.showPopup = showPopup;
window.closePopup = closePopup;
window.updateSidebarStatus = updateSidebarStatus;
